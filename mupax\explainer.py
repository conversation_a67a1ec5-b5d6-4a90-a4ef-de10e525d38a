import numpy as np
import pandas as pd
import itertools
from concurrent.futures import ThreadPoolExecutor
import warnings
from tqdm import tqdm
from sentence_transformers import SentenceTransformer, util
from transformers import pipeline
import torch
import random

# --- HELPER FUNCTIONS ---

def _generate_one_sample(args):
    """Generates a single masked sample for factual explanation (tabular/image)."""
    (seed, n_features, all_chunks, chunk_slices, padded_shape, 
     original_shape, mask_value, x_input_dtype) = args
    np.random.seed(seed)
    n_total_chunks = len(all_chunks)
    selected_indices = np.random.choice(n_total_chunks, n_features, replace=False)
    reconstructed_padded = np.full(padded_shape, mask_value, dtype=x_input_dtype)
    for idx in selected_indices:
        reconstructed_padded[chunk_slices[idx]] = all_chunks[idx]
    unpad_slicing = tuple(slice(0, s) for s in original_shape)
    return reconstructed_padded[unpad_slicing]

def _generate_one_counterfactual_sample(args):
    """
    Generates a single counterfactual sample for tabular/image data by
    keeping some original chunks and inserting prototype chunks.
    """
    (seed, x_input_chunks, chunk_prototypes, n_keep, n_insert,
     padded_shape, original_shape, chunk_slices, mask_value, x_input_dtype) = args
    
    np.random.seed(seed)
    
    n_total_chunks = len(x_input_chunks)
    all_indices = np.arange(n_total_chunks)
    
    keep_indices = np.random.choice(all_indices, size=n_keep, replace=False)
    
    remaining_indices = np.setdiff1d(all_indices, keep_indices, assume_unique=True)
    insert_indices = np.random.choice(remaining_indices, size=n_insert, replace=False)

    reconstructed_padded = np.full(padded_shape, mask_value, dtype=x_input_dtype)

    for idx in keep_indices:
        reconstructed_padded[chunk_slices[idx]] = x_input_chunks[idx]

    for idx in insert_indices:
        prototype_pool_for_chunk = chunk_prototypes[idx]
        prototype_to_insert = prototype_pool_for_chunk[np.random.randint(len(prototype_pool_for_chunk))]
        reconstructed_padded[chunk_slices[idx]] = prototype_to_insert

    unpad_slicing = tuple(slice(0, s) for s in original_shape)
    return reconstructed_padded[unpad_slicing]

 
def _generate_one_bon_text_augmentation(args):
    """
    Generates a single text augmentation based on the Best-of-N paper.
    """
    (seed, text, p_scramble, p_capitalize, p_noise) = args
    random.seed(seed)
    
    # 1. Character Scrambling
    words = text.split(' ')
    scrambled_words = []
    for word in words:
        if len(word) > 3 and random.random() < p_scramble:
            middle = list(word[1:-1])
            random.shuffle(middle)
            scrambled_words.append(word[0] + "".join(middle) + word[-1])
        else:
            scrambled_words.append(word)
    text = " ".join(scrambled_words)

    # 2. Random Capitalization
    capitalized_chars = []
    for char in text:
        if char.isalpha() and random.random() < p_capitalize:
            capitalized_chars.append(char.upper())
        else:
            capitalized_chars.append(char)
    text = "".join(capitalized_chars)

    # 3. Character Noising
    noised_chars = []
    for char in text:
        if 32 <= ord(char) <= 126 and random.random() < p_noise:
            new_ord = ord(char) + random.choice([-1, 1])
            # Ensure it stays within readable ASCII range
            if 32 <= new_ord <= 126:
                noised_chars.append(chr(new_ord))
            else:
                noised_chars.append(char)
        else:
            noised_chars.append(char)
    
    return "".join(noised_chars)


def _generate_one_intelligent_nlp_counterfactual(args):
    """
    Generates a single, grammatically plausible counterfactual prompt using a
    Masked Language Model (MLM) to fill in blanks.
    """
    (seed, original_tokens, attack_tokens, n_keep, n_insert, fill_mask_pipeline) = args

    tokenizer = fill_mask_pipeline.tokenizer
    np.random.seed(seed)
    prompt_len = len(original_tokens)
    
    if n_keep > prompt_len:
        warnings.warn(f"n_keep ({n_keep}) > prompt_len ({prompt_len}). Clamping n_keep to {prompt_len}.")
        n_keep = prompt_len
        n_insert = 0
        
    n_to_mask = prompt_len - n_keep
    all_indices = np.arange(prompt_len)
    keep_indices = np.random.choice(all_indices, size=n_keep, replace=False)
    mask_indices = np.setdiff1d(all_indices, keep_indices)

    n_semantic_replace = n_to_mask - n_insert
    
    masked_token_ids = original_tokens.copy()
    if len(mask_indices) > 0:
        masked_token_ids[mask_indices] = tokenizer.mask_token_id
    
    final_token_ids = masked_token_ids.copy()
    current_mask_indices = list(mask_indices)

    if n_insert > 0 and len(current_mask_indices) > 0:
        insert_positions = np.random.choice(current_mask_indices, size=min(n_insert, len(current_mask_indices)), replace=False)
        for pos in insert_positions:
            temp_prompt_ids = final_token_ids.copy()
            temp_prompt_ids[pos] = tokenizer.mask_token_id
            masked_prompt_str = tokenizer.decode(temp_prompt_ids, skip_special_tokens=False)
            
            valid_attack_tokens = [t for t in attack_tokens if t in tokenizer.vocab]
            if not valid_attack_tokens: continue

            predictions = fill_mask_pipeline(masked_prompt_str, targets=valid_attack_tokens)
            
            if predictions:
                best_fit_token = predictions.get('token_str') if isinstance(predictions, dict) else (predictions[0].get('token_str') if isinstance(predictions, list) and predictions else None)
                if best_fit_token:
                    final_token_ids[pos] = tokenizer.convert_tokens_to_ids(best_fit_token)
            
            if pos in current_mask_indices:
                current_mask_indices.remove(pos)

    if n_semantic_replace > 0 and len(current_mask_indices) > 0:
        for pos in current_mask_indices:
            temp_prompt_ids = final_token_ids.copy()
            temp_prompt_ids[pos] = tokenizer.mask_token_id
            masked_prompt_str = tokenizer.decode(temp_prompt_ids, skip_special_tokens=False)
            predictions = fill_mask_pipeline(masked_prompt_str, top_k=1)
            
            if predictions and isinstance(predictions, list) and isinstance(predictions[0], dict):
                best_replacement_token = predictions[0].get('token_str')
                if best_replacement_token:
                    final_token_ids[pos] = tokenizer.convert_tokens_to_ids(best_replacement_token)

    return final_token_ids

# --- CORE MUPAX CLASS ---

class Mupax:
    """
    A generic, N-dimensional implementation of the Mupax XAI algorithm.
    """
    def __init__(self, model_predict_fn, loss_fn):
        self.model_predict_fn = model_predict_fn
        self.loss_fn = loss_fn

    def explain(self, x_input, y_true, n_samples, n_features, chunk_spec, percentile=20, mask_value=0.0, batch_size=32, n_workers=4):
        padded_x, chunk_slices, all_chunks = self._prepare_chunks(x_input, chunk_spec, mask_value)
        n_total_chunks = len(all_chunks)
        if n_features > n_total_chunks:
            warnings.warn(f"n_features ({n_features}) > total chunks ({n_total_chunks}). Using n_features = {n_total_chunks}.")
            n_features = n_total_chunks
        worker_args = (n_features, np.array(all_chunks), chunk_slices, padded_x.shape, x_input.shape, mask_value, x_input.dtype)
        seeds = np.random.randint(0, 2**32 - 1, size=n_samples, dtype=np.int64)
        map_args = [(seed, *worker_args) for seed in seeds]
        with ThreadPoolExecutor(max_workers=n_workers) as executor:
            masked_samples = list(tqdm(executor.map(_generate_one_sample, map_args), total=n_samples, desc="Generating Samples"))
        masked_samples = np.array(masked_samples)
        all_preds = []
        for i in tqdm(range(0, n_samples, batch_size), desc="Getting Predictions"):
            preds = self.model_predict_fn(masked_samples[i:i+batch_size])
            all_preds.append(preds)
        all_preds = np.concatenate(all_preds, axis=0)
        losses = self.loss_fn(all_preds, y_true)
        loss_threshold = np.percentile(losses, percentile)
        selection_mask = losses <= loss_threshold
        if not np.any(selection_mask):
            warnings.warn("No samples met threshold. Using best sample.")
            selection_mask[np.argmin(losses)] = True
        selected_samples = masked_samples[selection_mask]
        selected_losses = losses[selection_mask]
        weights = 1.0 / (selected_losses + 1.0)
        if np.sum(weights) == 0:
            weights = None
        return np.average(selected_samples, axis=0, weights=weights).astype(x_input.dtype)

    def explain_counterfactual(self, x_input, y_target, prototype_pool, n_samples, n_keep, n_insert, chunk_spec, lambda_ins=1.0, mask_value=0.0, batch_size=32, n_workers=4):
        padded_x, chunk_slices, x_input_chunks = self._prepare_chunks(x_input, chunk_spec, mask_value)
        n_total_chunks = len(x_input_chunks)
        if n_keep + n_insert > n_total_chunks:
            raise ValueError(f"n_keep ({n_keep}) + n_insert ({n_insert}) > total chunks ({n_total_chunks})")
        if len(prototype_pool) == 0:
            raise ValueError(f"Prototype pool for target {y_target} is empty.")
        chunk_prototypes = {i: [] for i in range(n_total_chunks)}
        for p_sample in prototype_pool:
            _, _, p_chunks = self._prepare_chunks(p_sample, chunk_spec, mask_value)
            for i, chunk in enumerate(p_chunks):
                chunk_prototypes[i].append(chunk)
        worker_args = (np.array(x_input_chunks), chunk_prototypes, n_keep, n_insert, padded_x.shape, x_input.shape, chunk_slices, mask_value, x_input.dtype)
        seeds = np.random.randint(0, 2**32 - 1, size=n_samples, dtype=np.int64)
        map_args = [(seed, *worker_args) for seed in seeds]
        with ThreadPoolExecutor(max_workers=n_workers) as executor:
            cf_candidates = list(tqdm(executor.map(_generate_one_counterfactual_sample, map_args), total=n_samples, desc="Generating CF Candidates"))
        cf_candidates = np.array(cf_candidates)
        all_preds = []
        for i in tqdm(range(0, n_samples, batch_size), desc="Getting CF Predictions"):
            preds = self.model_predict_fn(cf_candidates[i:i + batch_size])
            all_preds.append(preds)
        all_preds = np.concatenate(all_preds, axis=0)
        predicted_labels = np.argmax(all_preds, axis=1)
        flip_mask = (predicted_labels == y_target)
        successful_samples = cf_candidates[flip_mask]
        if len(successful_samples) == 0:
            warnings.warn(f"No counterfactuals found for target '{y_target}'. Try increasing n_samples, n_keep, or n_insert.")
            return None
        cost = n_keep + lambda_ins * n_insert
        weight = 1.0 / (cost + 1.0)
        return np.average(successful_samples, axis=0, weights=np.full(len(successful_samples), weight)).astype(x_input.dtype)

    def _prepare_chunks(self, x_input, chunk_spec, mask_value):
        pad_width = []
        for i in range(len(chunk_spec)):
            remainder = x_input.shape[i] % chunk_spec[i]
            pad_amount = (chunk_spec[i] - remainder) % chunk_spec[i]
            pad_width.append((0, pad_amount))
        for i in range(len(chunk_spec), x_input.ndim):
            pad_width.append((0, 0))
        padded_x = np.pad(x_input, pad_width, 'constant', constant_values=mask_value)
        chunk_slices, all_chunks = [], []
        iter_ranges = [range(0, padded_x.shape[i], chunk_spec[i]) for i in range(len(chunk_spec))]
        for start_indices in itertools.product(*iter_ranges):
            slicing = [slice(start, start + chunk_spec[i]) for i, start in enumerate(start_indices)]
            for i in range(len(chunk_spec), x_input.ndim):
                slicing.append(slice(None))
            slicing = tuple(slicing)
            chunk_slices.append(slicing)
            all_chunks.append(padded_x[slicing])
        return padded_x, chunk_slices, all_chunks


# --- SPECIALIZED JAILBREAK EXPLAINER CLASS ---

class EmbeddingSimilarityJailbreakExplainer:
    def __init__(self, llm_pipeline, sentence_transformer_model, fill_mask_pipeline):
        self.llm_pipeline = llm_pipeline
        print(f"Loading sentence transformer model: {sentence_transformer_model}...")
        self.embedder = SentenceTransformer(sentence_transformer_model)
        self.fill_mask_pipeline = fill_mask_pipeline
        self.tokenizer = self.fill_mask_pipeline.tokenizer
        print("Models and tokenizers loaded.")
        self.target_embedding = None

    def _pipeline_predict_fn(self, prompts_batch):
        if self.target_embedding is None:
            raise RuntimeError("Target embedding has not been set. Call `find_jailbreak_prompt` first.")
        
        responses = []
        for p in prompts_batch:
            response_text = self.llm_pipeline.generate(p, max_new_tokens=256)
            responses.append(response_text)
        
        response_embeddings = self.embedder.encode(responses, convert_to_tensor=True)
        similarities = util.cos_sim(self.target_embedding, response_embeddings).cpu().numpy().flatten()
        
        similarities = np.clip(similarities, 0, 1)
        return similarities

    def find_jailbreak_prompt(self, harmful_prompt, target_answer, attack_phrases, n_keep, n_insert, n_samples=5000, **kwargs):
        print("--- Embedding Target Answer ---")
        self.target_embedding = self.embedder.encode(target_answer, convert_to_tensor=True)
        print(f"Target Answer:\n---\n{target_answer[:200]}...\n---")

        tokenized_prompt = np.array(self.tokenizer.encode(harmful_prompt, add_special_tokens=False))
        attack_token_strs = [word for phrase in attack_phrases for word in phrase.split()]
        
        worker_args = (tokenized_prompt, attack_token_strs, n_keep, n_insert, self.fill_mask_pipeline)
        seeds = np.random.randint(0, 2**32 - 1, size=n_samples, dtype=np.int64)
        map_args = [(seed, *worker_args) for seed in seeds]
        
        print("Generating intelligent counterfactual candidates...")
        with ThreadPoolExecutor(max_workers=kwargs.get('n_workers', 4)) as executor:
            cf_candidates_tokens = list(tqdm(executor.map(_generate_one_intelligent_nlp_counterfactual, map_args), total=n_samples, desc="Generating CF Candidates"))
        
        candidate_prompts = [self.tokenizer.decode(ids, skip_special_tokens=True).replace(self.tokenizer.pad_token, '') for ids in cf_candidates_tokens]

        print("Evaluating all candidate prompts...")
        all_scores = []
        batch_size = kwargs.get('batch_size', 16)
        for i in tqdm(range(0, len(candidate_prompts), batch_size), desc="Evaluating Batches"):
            batch_prompts = candidate_prompts[i:i + batch_size]
            scores = self._pipeline_predict_fn(batch_prompts)
            all_scores.extend(scores)
        
        predicted_scores = np.array(all_scores)

        if len(predicted_scores) == 0:
             print("No candidates were generated.")
             self.target_embedding = None
             return None, None
             
        best_candidate_index = np.argmax(predicted_scores)
        best_jailbreak_score = predicted_scores[best_candidate_index]
        best_jailbreak_prompt = candidate_prompts[best_candidate_index]

        print(f"\nDiscovered best jailbreak prompt with similarity score = {best_jailbreak_score:.4f}")
        self.target_embedding = None

        orig_tokens_decoded = harmful_prompt.split()
        jail_tokens_decoded = best_jailbreak_prompt.split()
        max_len = max(len(orig_tokens_decoded), len(jail_tokens_decoded))
        
        orig_padded = orig_tokens_decoded + [''] * (max_len - len(orig_tokens_decoded))
        jail_padded = jail_tokens_decoded + [''] * (max_len - len(jail_tokens_decoded))

        results_df = pd.DataFrame({
            'Original_Token': orig_padded,
            'Best_Jailbreak_Token': jail_padded,
        })
        
        print("\n--- Jailbreak Analysis ---")
        print(f"\nMost Effective Jailbreak Prompt Found:\n'{best_jailbreak_prompt}'")
        return results_df, best_jailbreak_prompt
    

class BoNJailbreakExplainer:
    """
    Implements the Best-of-N (BoN) Jailbreaking strategy.
    """
    def __init__(self, llm_pipeline, harm_classifier_fn):
        self.llm_pipeline = llm_pipeline
        self.harm_classifier_fn = harm_classifier_fn

    def find_jailbreak_prompt(self, harmful_prompt, N=100, p_scramble=0.6, p_capitalize=0.6, p_noise=0.06):
        """
        Applies N random augmentations to a prompt until a harmful response is elicited.

        Args:
            harmful_prompt (str): The initial harmful prompt.
            N (int): The maximum number of augmentations to try.
            p_scramble (float): Probability of scrambling characters in a word.
            p_capitalize (float): Probability of capitalizing a character.
            p_noise (float): Probability of noising a character.

        Returns:
            A tuple (successful_prompt, successful_response) if a jailbreak is found,
            otherwise (None, None).
        """
        print(f"--- Starting Best-of-N Jailbreaking with N={N} ---")
        for i in tqdm(range(N), desc="Trying BoN Augmentations"):
            seed = i # Use loop index for a different seed each time
            
            # 1. Augment the prompt
            augmented_prompt = _generate_one_bon_text_augmentation(
                (seed, harmful_prompt, p_scramble, p_capitalize, p_noise)
            )
            
            # 2. Query the model
            response = self.llm_pipeline.generate(augmented_prompt, max_new_tokens=256)
            
            # 3. Classify the response
            is_harmful = self.harm_classifier_fn(response)
            
            # 4. Check for success
            if is_harmful:
                print(f"\nJailbreak successful after {i+1} attempts!")
                print(f"Successful Prompt: '{augmented_prompt}'")
                print(f"Harmful Response:\n---\n{response}\n---")
                return augmented_prompt, response

        print("\nBoN Jailbreaking finished. No successful jailbreak found within N samples.")
        return None, None
